{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "main": "dist/main/main.cjs", "scripts": {"dev": "vite", "build": "npm run build:main && npm run build:renderer", "build:renderer": "vite build", "build:icon": "node scripts/convert-to-ico.mjs", "build:main": "rimraf dist/main && tsc -p tsconfig.main.json && mkdirp dist/main && copyfiles src/main/preload.js dist/main/ && copyfiles public/mixfade_icon-icoext.ico dist/main/ && node scripts/rename-main.mjs", "start": "electron .", "electron:dev": "npm run build:main && concurrently -k \"vite\" \"wait-on tcp:5173 && electron . --dev\"", "electron:start": "electron . --dev", "dist": "npm run build && electron-builder", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "wavesurfer.js": "^7.7.3"}, "homepage": "./", "build": {"appId": "com.mixfade.app", "productName": "MixFade", "files": ["dist/**/*", "public/**/*", "node_modules/**/*"], "directories": {"buildResources": "assets"}, "win": {"target": "nsis", "icon": "public/mixfade_icon-icoext.ico"}, "mac": {"target": "dmg", "icon": "public/mixfade_icon.png"}, "linux": {"target": ["AppImage", "deb"], "icon": "public/mixfade_icon.png"}}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/node": "^24.0.8", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "concurrently": "^8.2.2", "copyfiles": "^2.4.1", "cross-env": "^7.0.3", "electron": "^33.4.11", "electron-builder": "^25.1.8", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.5.6", "rimraf": "^6.0.1", "sharp": "^0.34.2", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.3", "wait-on": "^7.2.0"}}