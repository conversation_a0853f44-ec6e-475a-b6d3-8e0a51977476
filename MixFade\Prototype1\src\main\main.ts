import { app, BrowserWindow, Menu, MenuItemConstructorOptions, ipcMain, nativeImage } from 'electron';
import * as path from 'path';
import * as fs from 'fs';

const isDev = process.argv.includes('--dev');

let mainWindow: BrowserWindow | null = null;

function createWindow() {
  // Create the browser window
  const { nativeImage, app, BrowserWindow } = require('electron');
  
  // Set app user model ID for Windows taskbar
  if (process.platform === 'win32') {
    app.setAppUserModelId('com.mixfade.app');
  }
  
  // Function to set window icon
  const setWindowIcon = (window: any) => {
    if (!window) return false;
    
    try {
      // Try multiple possible icon locations and formats
      const iconPaths = [
        path.join(__dirname, '..', '..', 'public', 'mixfade_icon-icoext.ico'),
        path.join(process.resourcesPath, 'mixfade_icon-icoext.ico'),
      ];
      
      for (const iconPath of iconPaths) {
        try {
          if (require('fs').existsSync(iconPath)) {
            const icon = nativeImage.createFromPath(iconPath);
            if (icon && typeof icon.getSize === 'function') {
              window.setIcon(icon);
              console.log('Successfully set window icon from:', iconPath);
              return true;
            }
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          console.warn(`Failed to load icon from ${iconPath}:`, errorMessage);
        }
      }
      
      console.warn('Could not find a valid icon file');
      return false;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('Error setting window icon:', errorMessage);
      return false;
    }
  };
  
  // Initialize mainWindow as null
  let mainWindow: any = null;
  
  // Create window with default settings
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: isDev 
        ? path.join(__dirname, '..', '..', 'public', 'preload.js')
        : path.join(__dirname, 'preload.js'),
      sandbox: true,
      webSecurity: true
    },
    show: false,
    titleBarStyle: 'default' as const,
    backgroundColor: '#ffffff',
    frame: true,
    autoHideMenuBar: false,
    title: 'MixFade'
  });
  
  // Set window icon after creation
  setWindowIcon(mainWindow);

  // Load the app
  const indexPath = isDev 
    ? 'http://localhost:5173/' 
    : `file://${path.join(__dirname, '../renderer/index.html')}`;
  
  mainWindow.loadURL(indexPath).catch((err: any) => {
    console.error('Failed to load URL:', err);
  });

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow?.show();
  });

  // Open DevTools in development
  if (isDev && mainWindow) {
    mainWindow.webContents.openDevTools();
  }

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// This method will be called when Electron has finished initialization
app.whenReady().then(() => {
  // Set app icon globally with Windows-specific handling
  const globalIconPaths = [
    // Try PNG first for better compatibility
    path.join(__dirname, '..', '..', 'public', 'mixfade_icon.png'),
    path.join(__dirname, '..', 'public', 'mixfade_icon.png'),
    path.join(process.cwd(), 'public', 'mixfade_icon.png'),
    // Then ICO files
    path.join(__dirname, '..', '..', 'public', 'mixfade_icon-icoext.ico'),
    path.join(__dirname, '..', 'public', 'mixfade_icon-icoext.ico'),
    path.join(process.cwd(), 'public', 'mixfade_icon-icoext.ico'),
  ];

  const globalIconPath = globalIconPaths.find(p => fs.existsSync(p));
  if (globalIconPath) {
    try {
      const appIcon = nativeImage.createFromPath(globalIconPath);
      if (!appIcon.isEmpty()) {
        // Set Windows-specific app ID for proper taskbar grouping
        if (process.platform === 'win32') {
          app.setAppUserModelId('com.mixfade.app');
        }
        console.log('Set global app icon from:', globalIconPath);
      }
    } catch (error) {
      console.warn('Failed to set global app icon:', error);
    }
  }

  createWindow();

  // Create application menu
  createMenu();

  // Set up IPC handlers
  setupIPC();

  app.on('activate', () => {
    // On macOS, re-create window when dock icon is clicked
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// Set up IPC handlers for window controls
function setupIPC() {
  // Window control handlers
  ipcMain.on('window-minimize', () => {
    if (mainWindow) {
      mainWindow.minimize();
    }
  });

  ipcMain.on('window-maximize', () => {
    if (mainWindow) {
      if (mainWindow.isMaximized()) {
        mainWindow.unmaximize();
      } else {
        mainWindow.maximize();
      }
    }
  });

  ipcMain.on('window-close', () => {
    if (mainWindow) {
      mainWindow.close();
    }
  });
}

// Quit when all windows are closed
app.on('window-all-closed', () => {
  // On macOS, keep app running even when all windows are closed
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Create application menu
function createMenu() {
  const template: MenuItemConstructorOptions[] = [
    {
      label: 'File',
      submenu: [
        {
          label: 'Refresh',
          accelerator: 'CmdOrCtrl+R',
          click: () => {
            if (mainWindow) {
              mainWindow.reload();
            }
          }
        },
        {
          label: 'Toggle DevTools',
          accelerator: 'F12',
          click: () => {
            if (mainWindow) {
              mainWindow.webContents.toggleDevTools();
            }
          }
        },
        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        { role: 'delete' },
        { type: 'separator' },
        { role: 'selectAll' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      role: 'help',
      submenu: [
        {
          label: 'Learn More',
          click: async () => {
            const { shell } = require('electron');
            await shell.openExternal('https://electronjs.org');
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// Set up logging
const logFile = path.join(app.getPath('userData'), 'main.log');
const logStream = fs.createWriteStream(logFile, { flags: 'a' });

function log(message: string, ...args: any[]): void {
  const timestamp = new Date().toISOString();
  console.log(`[Main] ${message}`, ...args);
  
  // Log to file in production
  if (!isDev) {
    const logPath = path.join(app.getPath('logs'), 'mixfade-main.log');
    const logMessage = `[${timestamp}] ${message} ${args.length ? JSON.stringify(args) : ''}\n`;
    fs.appendFileSync(logPath, logMessage, 'utf8');
  }
}

// Clean up on exit
app.on('will-quit', () => {
  log('Application quitting...');
  logStream.end();
});
