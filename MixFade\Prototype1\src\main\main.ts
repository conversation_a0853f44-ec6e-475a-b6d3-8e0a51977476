import { app, BrowserWindow, Menu, MenuItemConstructorOptions, ipcMain, nativeImage } from 'electron';
import * as path from 'path';
import * as fs from 'fs';

const isDev = process.argv.includes('--dev');

let mainWindow: BrowserWindow | null = null;

function createWindow() {
  // Create the browser window
  
  // Set app user model ID for Windows taskbar
  if (process.platform === 'win32') {
    app.setAppUserModelId('com.mixfade.app');
  }
  
  // Function to set window icon (backup method)
  const setWindowIcon = (window: any) => {
    if (!window) return false;

    try {
      // Try multiple possible icon locations and formats
      const iconPaths = [
        path.join(__dirname, '..', '..', 'public', 'mixfade_icon-icoext.ico'),
        path.join(__dirname, '..', '..', 'public', 'mixfade_icon.png'),
        path.join(process.resourcesPath, 'mixfade_icon-icoext.ico'),
        path.join(process.resourcesPath, 'mixfade_icon.png'),
      ];

      for (const iconPath of iconPaths) {
        try {
          if (fs.existsSync(iconPath)) {
            const icon = nativeImage.createFromPath(iconPath);
            if (icon && !icon.isEmpty()) {
              window.setIcon(icon);
              console.log('Successfully set window icon from:', iconPath);
              return true;
            }
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          console.warn(`Failed to load icon from ${iconPath}:`, errorMessage);
        }
      }

      console.warn('Could not find a valid icon file');
      return false;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('Error setting window icon:', errorMessage);
      return false;
    }
  };
  
  // Initialize mainWindow as null
  let mainWindow: any = null;
  
  // Set up icon path
  const iconPath = isDev
    ? path.join(__dirname, '..', '..', 'public', 'mixfade_icon-icoext.ico')
    : path.join(process.resourcesPath, 'mixfade_icon-icoext.ico');

  // Create window with modern custom title bar settings
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    icon: iconPath, // Set icon directly in constructor
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: isDev
        ? path.join(__dirname, '..', '..', 'public', 'preload.js')
        : path.join(__dirname, 'preload.js'),
      sandbox: true,
      webSecurity: true
    },
    show: false,
    // Modern custom title bar configuration
    titleBarStyle: 'hidden',
    // Add native window controls for Windows/Linux
    ...(process.platform !== 'darwin' ? {
      titleBarOverlay: {
        color: '#0f172a', // Match your app's dark theme
        symbolColor: '#ffffff',
        height: 40
      }
    } : {}),
    backgroundColor: '#0f172a', // Match your gradient background
    frame: true,
    autoHideMenuBar: true, // Hide native menu bar
    title: 'MixFade',
    // macOS specific traffic light positioning
    ...(process.platform === 'darwin' ? {
      trafficLightPosition: { x: 20, y: 15 }
    } : {})
  });
  
  // Set window icon after creation
  setWindowIcon(mainWindow);

  // Load the app
  const indexPath = isDev 
    ? 'http://localhost:5173/' 
    : `file://${path.join(__dirname, '../renderer/index.html')}`;
  
  mainWindow.loadURL(indexPath).catch((err: any) => {
    console.error('Failed to load URL:', err);
  });

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow?.show();
  });

  // Open DevTools in development
  if (isDev && mainWindow) {
    mainWindow.webContents.openDevTools();
  }

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// Set up IPC handlers for window controls
function setupIPC() {
  // Window control handlers
  ipcMain.on('window-minimize', () => {
    if (mainWindow) {
      mainWindow.minimize();
    }
  });

  ipcMain.on('window-maximize', () => {
    if (mainWindow) {
      if (mainWindow.isMaximized()) {
        mainWindow.unmaximize();
      } else {
        mainWindow.maximize();
      }
    }
  });

  ipcMain.on('window-close', () => {
    if (mainWindow) {
      mainWindow.close();
    }
  });
}

// This method will be called when Electron has finished initialization
app.whenReady().then(() => {
  createWindow();

  // Create application menu
  createMenu();

  // Set up IPC handlers
  setupIPC();

  app.on('activate', () => {
    // On macOS, re-create window when dock icon is clicked
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// Quit when all windows are closed
app.on('window-all-closed', () => {
  // On macOS, keep app running even when all windows are closed
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Create application menu (minimal, mostly for macOS and keyboard shortcuts)
function createMenu() {
  const template: MenuItemConstructorOptions[] = [
    // macOS app menu
    ...(process.platform === 'darwin' ? [{
      label: app.getName(),
      submenu: [
        { role: 'about' as const },
        { type: 'separator' as const },
        { role: 'services' as const },
        { type: 'separator' as const },
        { role: 'hide' as const },
        { role: 'hideOthers' as const },
        { role: 'unhide' as const },
        { type: 'separator' as const },
        { role: 'quit' as const }
      ]
    }] : []),
    {
      label: 'File',
      submenu: [
        {
          label: 'Open Audio Files...',
          accelerator: 'CmdOrCtrl+O',
          click: () => {
            // This will be handled by your React app via IPC
            if (mainWindow) {
              mainWindow.webContents.send('menu-open-files');
            }
          }
        },
        { type: 'separator' },
        ...(process.platform !== 'darwin' ? [
          {
            label: 'Exit',
            accelerator: 'Ctrl+Q',
            click: () => app.quit()
          }
        ] : [])
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' as const },
        { role: 'redo' as const },
        { type: 'separator' as const },
        { role: 'cut' as const },
        { role: 'copy' as const },
        { role: 'paste' as const }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' as const },
        { role: 'toggleDevTools' as const },
        { type: 'separator' as const },
        { role: 'resetZoom' as const },
        { role: 'zoomIn' as const },
        { role: 'zoomOut' as const },
        { type: 'separator' as const },
        { role: 'togglefullscreen' as const }
      ]
    },
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' as const },
        { role: 'close' as const },
        ...(process.platform === 'darwin' ? [
          { type: 'separator' as const },
          { role: 'front' as const }
        ] : [])
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);

  // On macOS, always show menu bar. On Windows/Linux, hide it since we have custom title bar
  if (process.platform === 'darwin') {
    Menu.setApplicationMenu(menu);
  } else {
    Menu.setApplicationMenu(null); // Hide menu bar on Windows/Linux
  }
}

// Set up logging
const logFile = path.join(app.getPath('userData'), 'main.log');
const logStream = fs.createWriteStream(logFile, { flags: 'a' });

function log(message: string, ...args: any[]): void {
  const timestamp = new Date().toISOString();
  console.log(`[Main] ${message}`, ...args);
  
  // Log to file in production
  if (!isDev) {
    const logPath = path.join(app.getPath('logs'), 'mixfade-main.log');
    const logMessage = `[${timestamp}] ${message} ${args.length ? JSON.stringify(args) : ''}\n`;
    fs.appendFileSync(logPath, logMessage, 'utf8');
  }
}

// Clean up on exit
app.on('will-quit', () => {
  log('Application quitting...');
  logStream.end();
});
