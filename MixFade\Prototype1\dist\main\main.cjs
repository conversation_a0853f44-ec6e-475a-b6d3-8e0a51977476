"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const isDev = process.argv.includes('--dev');
let mainWindow = null;
function createWindow() {
    // Create the browser window
    const { nativeImage, app, BrowserWindow } = require('electron');
    // Set app user model ID for Windows taskbar
    if (process.platform === 'win32') {
        app.setAppUserModelId('com.mixfade.app');
    }
    // Function to set window icon
    const setWindowIcon = (window) => {
        if (!window)
            return false;
        try {
            // Try multiple possible icon locations and formats
            const iconPaths = [
                path.join(__dirname, '..', '..', 'public', 'mixfade_icon-icoext.ico'),
                path.join(process.resourcesPath, 'mixfade_icon-icoext.ico'),
            ];
            for (const iconPath of iconPaths) {
                try {
                    if (require('fs').existsSync(iconPath)) {
                        const icon = nativeImage.createFromPath(iconPath);
                        if (icon && typeof icon.getSize === 'function') {
                            window.setIcon(icon);
                            console.log('Successfully set window icon from:', iconPath);
                            return true;
                        }
                    }
                }
                catch (error) {
                    const errorMessage = error instanceof Error ? error.message : String(error);
                    console.warn(`Failed to load icon from ${iconPath}:`, errorMessage);
                }
            }
            console.warn('Could not find a valid icon file');
            return false;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            console.error('Error setting window icon:', errorMessage);
            return false;
        }
    };
    // Initialize mainWindow as null
    let mainWindow = null;
    // Create window with modern custom title bar settings
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        minWidth: 800,
        minHeight: 600,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: isDev
                ? path.join(__dirname, '..', '..', 'public', 'preload.js')
                : path.join(__dirname, 'preload.js'),
            sandbox: true,
            webSecurity: true
        },
        show: false,
        // Modern custom title bar configuration
        titleBarStyle: 'hidden',
        // Add native window controls for Windows/Linux
        ...(process.platform !== 'darwin' ? {
            titleBarOverlay: {
                color: '#0f172a', // Match your app's dark theme
                symbolColor: '#ffffff',
                height: 40
            }
        } : {}),
        backgroundColor: '#0f172a', // Match your gradient background
        frame: true,
        autoHideMenuBar: true, // Hide native menu bar
        title: 'MixFade',
        // macOS specific traffic light positioning
        ...(process.platform === 'darwin' ? {
            trafficLightPosition: { x: 20, y: 15 }
        } : {})
    });
    // Set window icon after creation
    setWindowIcon(mainWindow);
    // Load the app
    const indexPath = isDev
        ? 'http://localhost:5173/'
        : `file://${path.join(__dirname, '../renderer/index.html')}`;
    mainWindow.loadURL(indexPath).catch((err) => {
        console.error('Failed to load URL:', err);
    });
    // Show window when ready
    mainWindow.once('ready-to-show', () => {
        mainWindow?.show();
    });
    // Open DevTools in development
    if (isDev && mainWindow) {
        mainWindow.webContents.openDevTools();
    }
    // Handle window closed
    mainWindow.on('closed', () => {
        mainWindow = null;
    });
}
// Set up IPC handlers for window controls
function setupIPC() {
    // Window control handlers
    electron_1.ipcMain.on('window-minimize', () => {
        if (mainWindow) {
            mainWindow.minimize();
        }
    });
    electron_1.ipcMain.on('window-maximize', () => {
        if (mainWindow) {
            if (mainWindow.isMaximized()) {
                mainWindow.unmaximize();
            }
            else {
                mainWindow.maximize();
            }
        }
    });
    electron_1.ipcMain.on('window-close', () => {
        if (mainWindow) {
            mainWindow.close();
        }
    });
}
// This method will be called when Electron has finished initialization
electron_1.app.whenReady().then(() => {
    createWindow();
    // Create application menu
    createMenu();
    // Set up IPC handlers
    setupIPC();
    electron_1.app.on('activate', () => {
        // On macOS, re-create window when dock icon is clicked
        if (electron_1.BrowserWindow.getAllWindows().length === 0) {
            createWindow();
        }
    });
});
// Quit when all windows are closed
electron_1.app.on('window-all-closed', () => {
    // On macOS, keep app running even when all windows are closed
    if (process.platform !== 'darwin') {
        electron_1.app.quit();
    }
});
// Create application menu (minimal, mostly for macOS and keyboard shortcuts)
function createMenu() {
    const template = [
        // macOS app menu
        ...(process.platform === 'darwin' ? [{
                label: electron_1.app.getName(),
                submenu: [
                    { role: 'about' },
                    { type: 'separator' },
                    { role: 'services' },
                    { type: 'separator' },
                    { role: 'hide' },
                    { role: 'hideOthers' },
                    { role: 'unhide' },
                    { type: 'separator' },
                    { role: 'quit' }
                ]
            }] : []),
        {
            label: 'File',
            submenu: [
                {
                    label: 'Open Audio Files...',
                    accelerator: 'CmdOrCtrl+O',
                    click: () => {
                        // This will be handled by your React app via IPC
                        if (mainWindow) {
                            mainWindow.webContents.send('menu-open-files');
                        }
                    }
                },
                { type: 'separator' },
                ...(process.platform !== 'darwin' ? [
                    {
                        label: 'Exit',
                        accelerator: 'Ctrl+Q',
                        click: () => electron_1.app.quit()
                    }
                ] : [])
            ]
        },
        {
            label: 'Edit',
            submenu: [
                { role: 'undo' },
                { role: 'redo' },
                { type: 'separator' },
                { role: 'cut' },
                { role: 'copy' },
                { role: 'paste' }
            ]
        },
        {
            label: 'View',
            submenu: [
                { role: 'reload' },
                { role: 'toggleDevTools' },
                { type: 'separator' },
                { role: 'resetZoom' },
                { role: 'zoomIn' },
                { role: 'zoomOut' },
                { type: 'separator' },
                { role: 'togglefullscreen' }
            ]
        },
        {
            label: 'Window',
            submenu: [
                { role: 'minimize' },
                { role: 'close' },
                ...(process.platform === 'darwin' ? [
                    { type: 'separator' },
                    { role: 'front' }
                ] : [])
            ]
        }
    ];
    const menu = electron_1.Menu.buildFromTemplate(template);
    // On macOS, always show menu bar. On Windows/Linux, hide it since we have custom title bar
    if (process.platform === 'darwin') {
        electron_1.Menu.setApplicationMenu(menu);
    }
    else {
        electron_1.Menu.setApplicationMenu(null); // Hide menu bar on Windows/Linux
    }
}
// Set up logging
const logFile = path.join(electron_1.app.getPath('userData'), 'main.log');
const logStream = fs.createWriteStream(logFile, { flags: 'a' });
function log(message, ...args) {
    const timestamp = new Date().toISOString();
    console.log(`[Main] ${message}`, ...args);
    // Log to file in production
    if (!isDev) {
        const logPath = path.join(electron_1.app.getPath('logs'), 'mixfade-main.log');
        const logMessage = `[${timestamp}] ${message} ${args.length ? JSON.stringify(args) : ''}\n`;
        fs.appendFileSync(logPath, logMessage, 'utf8');
    }
}
// Clean up on exit
electron_1.app.on('will-quit', () => {
    log('Application quitting...');
    logStream.end();
});
//# sourceMappingURL=main.js.map