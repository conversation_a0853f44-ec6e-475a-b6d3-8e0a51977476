export interface ElectronAPI {
  minimize: () => void;
  maximize: () => void;
  close: () => void;
  onMenuOpenFiles: (callback: () => void) => void;
  removeMenuOpenFilesListener: (callback: () => void) => void;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
    api: {
      send: (channel: string, data?: any) => void;
      receive: (channel: string, func: (...args: any[]) => void) => void;
      removeListener: (channel: string, listener: (...args: any[]) => void) => void;
    };
  }
}

export {};
