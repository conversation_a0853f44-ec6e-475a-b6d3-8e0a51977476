import React from 'react';
import { Settings, Minimize2, Square, X } from 'lucide-react';

interface TitleBarProps {
  title?: string;
  onSettingsClick?: () => void;
}

export function TitleBar({ title = 'MixFade', onSettingsClick }: TitleBarProps) {
  const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;

  const handleMinimize = () => {
    if (window.electronAPI) {
      window.electronAPI.minimize();
    }
  };

  const handleMaximize = () => {
    if (window.electronAPI) {
      window.electronAPI.maximize();
    }
  };

  const handleClose = () => {
    if (window.electronAPI) {
      window.electronAPI.close();
    }
  };

  return (
    <div className="flex items-center justify-between h-10 bg-slate-900/80 backdrop-blur-sm border-b border-slate-700/50 select-none">
      {/* Left side - Logo and Title */}
      <div className="flex items-center px-4 app-region-drag flex-1">
        {/* Leave space for macOS traffic lights */}
        <div className={`flex items-center ${isMac ? 'ml-16' : ''}`}>
          {/* App Icon */}
          <div className="w-6 h-6 rounded-md bg-gradient-to-r from-emerald-500 to-purple-500 flex items-center justify-center mr-3">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stopColor="white" />
                  <stop offset="100%" stopColor="white" />
                </linearGradient>
              </defs>
              <path 
                d="M12 2L2 7L12 12L22 7L12 2Z" 
                stroke="url(#iconGradient)" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
              <path 
                d="M2 17L12 22L22 17" 
                stroke="url(#iconGradient)" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
              <path 
                d="M2 12L12 17L22 12" 
                stroke="url(#iconGradient)" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
            </svg>
          </div>
          
          {/* App Title */}
          <span className="text-white font-semibold text-sm tracking-wide">
            {title}
          </span>
        </div>
      </div>

      {/* Center - Could be used for tabs or current file name */}
      <div className="flex-1 flex justify-center app-region-drag">
        {/* This space can be used for showing current project/file */}
      </div>

      {/* Right side - Controls */}
      <div className="flex items-center px-4 space-x-2">
        {/* Settings Button */}
        <button
          onClick={onSettingsClick}
          className="app-region-no-drag p-1.5 rounded-md hover:bg-slate-700/50 transition-colors text-slate-400 hover:text-white"
          title="Settings"
        >
          <Settings size={16} />
        </button>

        {/* Window Controls (Windows/Linux only) */}
        {!isMac && (
          <div className="flex items-center space-x-1 ml-2">
            <button
              onClick={handleMinimize}
              className="app-region-no-drag p-1.5 rounded-md hover:bg-slate-700/50 transition-colors text-slate-400 hover:text-white"
              title="Minimize"
            >
              <Minimize2 size={14} />
            </button>
            <button
              onClick={handleMaximize}
              className="app-region-no-drag p-1.5 rounded-md hover:bg-slate-700/50 transition-colors text-slate-400 hover:text-white"
              title="Maximize"
            >
              <Square size={14} />
            </button>
            <button
              onClick={handleClose}
              className="app-region-no-drag p-1.5 rounded-md hover:bg-red-600 transition-colors text-slate-400 hover:text-white"
              title="Close"
            >
              <X size={14} />
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
