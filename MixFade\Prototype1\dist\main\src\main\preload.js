// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
const { contextBridge, ipc<PERSON>enderer } = require('electron');

// White-listed channels
const validChannels = [
  'app-version',
  'app-name',
  'app-path',
  'window-minimize',
  'window-maximize',
  'window-close',
  'menu-open-files'
];

// Expose protected methods
contextBridge.exposeInMainWorld(
  'api', {
    // Renderer to main
    send: (channel, data) => {
      if (validChannels.includes(channel)) {
        ipcRenderer.send(channel, data);
      }
    },
    // Main to renderer
    receive: (channel, func) => {
      if (validChannels.includes(channel)) {
        // Deliberately strip event as it includes `sender`
        ipcRenderer.on(channel, (event, ...args) => func(...args));
      }
    },
    // Remove listener
    removeListener: (channel, listener) => {
      if (validChannels.includes(channel)) {
        ipcRenderer.removeListener(channel, listener);
      }
    }
  }
);

// Expose window controls for custom title bar
contextBridge.exposeInMainWorld(
  'electronAPI', {
    minimize: () => ipcRenderer.send('window-minimize'),
    maximize: () => ipcRenderer.send('window-maximize'),
    close: () => ipcRenderer.send('window-close'),
    onMenuOpenFiles: (callback) => ipcRenderer.on('menu-open-files', callback),
    removeMenuOpenFilesListener: (callback) => ipcRenderer.removeListener('menu-open-files', callback)
  }
);
