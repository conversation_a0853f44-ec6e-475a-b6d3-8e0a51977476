import { useEffect } from 'react';

interface KeyboardShortcuts {
  [key: string]: () => void;
}

export function useKeyboardShortcuts(handlers: KeyboardShortcuts) {
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Build key combination string
      const parts: string[] = [];
      
      if (e.ctrlKey || e.metaKey) parts.push(navigator.platform.includes('Mac') ? 'cmd' : 'ctrl');
      if (e.shiftKey) parts.push('shift');
      if (e.altKey) parts.push('alt');
      
      // Add the actual key
      const key = e.key.toLowerCase();
      if (key !== 'control' && key !== 'shift' && key !== 'alt' && key !== 'meta') {
        parts.push(key);
      }
      
      const combination = parts.join('+');
      
      if (handlers[combination]) {
        e.preventDefault();
        handlers[combination]();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handlers]);
}

// Common shortcuts for audio apps
export const commonShortcuts = {
  // File operations
  'ctrl+o': 'Open File',
  'ctrl+s': 'Save Project',
  'ctrl+shift+s': 'Save As',
  'ctrl+n': 'New Project',
  
  // Playback
  'space': 'Play/Pause',
  'ctrl+space': 'Stop',
  'ctrl+r': 'Record',
  
  // View
  'ctrl+b': 'Toggle Sidebar',
  'f11': 'Toggle Fullscreen',
  'ctrl+shift+p': 'Command Palette',
  'ctrl+p': 'Quick Open',
  
  // Audio
  'ctrl+m': 'Mute',
  'ctrl+shift+m': 'Solo',
  'ctrl+z': 'Undo',
  'ctrl+y': 'Redo',
};
